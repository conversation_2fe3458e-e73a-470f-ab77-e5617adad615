import React, { useEffect, useState } from "react";
import Navigation from "./Navigation";
import { motion, useScroll, useTransform, MotionValue } from "framer-motion";
import { useLocation } from "react-router-dom";
import { FooterLink, SocialButton } from "./FooterComponents";
import {
  FaLinkedin,
  FaFacebook,
  FaYoutube,
  FaPhoneAlt,
  FaEnvelope,
  FaMapMarkerAlt,
  FaArrowRight
} from "react-icons/fa";

interface LayoutProps {
  children: React.ReactNode;
}

const currentYear = new Date().getFullYear();

// Animation variants for consistent motion

// New footer animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10
    }
  }
};

// Page transition overlayl
const PageTransition = () => (
  <motion.div
    initial={{ opacity: 1, scale: 1.05 }}
    animate={{ opacity: 0, scale: 1 }}
    transition={{ duration: 0.8, ease: "easeInOut" }}
    className="fixed inset-0 z-50 pointer-events-none bg-gradient-to-br from-slate-900 via-black to-slate-800"
  >
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.1)_0%,transparent_50%)]" />
    <div className="absolute inset-0 flex items-center justify-center">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="relative"
      >
        <span className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent font-['Open_Sans']">
          ATANDRA
        </span>
        <div className="absolute -inset-4 bg-blue-500/20 blur-xl -z-10" />
      </motion.div>
    </div>
  </motion.div>
);

// Scroll progress indicator
const ScrollProgress = ({ scaleX }: { scaleX: MotionValue<number> }) => (
  <div className="fixed top-0 left-0 right-0 z-50">
    <motion.div
      style={{ scaleX, transformOrigin: "0%" }}
      className="h-1 bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg"
    />
  </div>
);









// Colorful Top Border Component
const ColorfulTopBorder = () => (
  <motion.div
    className="fixed top-0 left-0 w-full h-1 sm:h-1.5 md:h-2 flex z-[60] shadow-sm"
    initial={{ opacity: 0, scaleX: 0 }}
    animate={{ opacity: 1, scaleX: 1 }}
    transition={{ duration: 1.2, ease: "easeOut" }}
  >
    <motion.div className="flex-1" style={{ backgroundColor: '#FFFF00' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.1 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#FF4500' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.15 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#000000' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.2 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#808080' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.25 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#008000' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.3 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#87CEEB' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.35 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#FFFF00' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.4 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#800000' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.45 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#FF4500' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.5 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#000000' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.55 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#808080' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.6 }}></motion.div>
    <motion.div className="flex-1" style={{ backgroundColor: '#008000' }} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.65 }}></motion.div>
  </motion.div>
);

// Main Layout Component
const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [scrollY, setScrollY] = useState(0);
  const { scrollYProgress } = useScroll();
  const location = useLocation();
  const isHomePage = location.pathname === "/";
  const scaleX = useTransform(scrollYProgress, [0, 1], [0, 1]);

  useEffect(() => {
    const handleScroll = () => {
      requestAnimationFrame(() => setScrollY(window.scrollY));
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);



  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-950 via-black to-slate-900 text-white">
      {/* Colorful Top Border */}
      <ColorfulTopBorder />

      <PageTransition />

      {!isHomePage && <ScrollProgress scaleX={scaleX} />}

      <Navigation scrollY={scrollY} />

      <main className="relative">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-slate-900 py-16">
        <div className="container mx-auto px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="max-w-7xl mx-auto"
          >


            {/* Main content section */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
              {/* Company overview */}
              <motion.div variants={itemVariants}>
                <h3 className="text-xl sm:text-2xl font-bold text-white mb-6 pb-2 border-b border-blue-800/50 font-['Open_Sans']">About Us</h3>
                <p className="text-white text-lg sm:text-xl mb-6 leading-relaxed text-justify font-['Open_Sans'] tracking-normal" style={{ wordSpacing: 'normal', textAlignLast: 'left' }}>
                  KRYKARD is India's leading power conditioning brand. Atandra Energy Pvt. Ltd., headquartered in Chennai, draws upon a rich foundation of more than 40+ years of expertise in the realm of Power & Energy Management.
                </p>
                <motion.a
                  href="/about/company"
                  className="inline-flex items-center gap-1 text-white hover:text-blue-400 group transition-colors duration-300 font-['Open_Sans'] font-medium"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                  <span>Read our story</span>
                  <FaArrowRight className="text-xs group-hover:translate-x-1 transition-transform duration-300" />
                </motion.a>
              </motion.div>

              {/* Products links */}
              <motion.div variants={itemVariants}>
                <h3 className="text-xl sm:text-2xl font-bold text-white mb-6 pb-2 border-b border-blue-800/50 font-['Open_Sans']">Products</h3>
                <ul className="space-y-2 font-['Open_Sans']">
                  <li><FooterLink href="/measure">Testing & Measurement</FooterLink></li>
                  <li><FooterLink href="/protect/ups">Online UPS</FooterLink></li>
                  <li><FooterLink href="/protect/servo-stabilizers">Power Conditioners</FooterLink></li>
                  <li><FooterLink href="/protect/static-stabilizers">Static Stabilizers</FooterLink></li>
                  <li><FooterLink href="/measure/power-quality-analyzers">Power Quality Analyzers</FooterLink></li>
                </ul>
              </motion.div>

              {/* Quick links */}
              <motion.div variants={itemVariants}>
                <h3 className="text-xl sm:text-2xl font-bold text-white mb-6 pb-2 border-b border-blue-800/50 font-['Open_Sans']">Quick Links</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-2">
                  <div>
                    <ul className="space-y-2 font-['Open_Sans']">
                      <li><FooterLink href="/">Home</FooterLink></li>
                      <li><FooterLink href="/about">About</FooterLink></li>
                      <li><FooterLink href="/contact/service">Service Care</FooterLink></li>
                      <li><FooterLink href="/contact">Contact Us</FooterLink></li>
                      <li><FooterLink href="/contact/service">Support</FooterLink></li>
                    </ul>
                  </div>
                </div>
              </motion.div>

              {/* Contact information */}
              <motion.div variants={itemVariants}>
                <h3 className="text-xl sm:text-2xl font-bold text-white mb-6 pb-2 border-b border-blue-800/50 font-['Open_Sans']">Contact Us</h3>
                <address className="not-italic space-y-4 text-white text-lg sm:text-xl font-['Open_Sans']">
                  <div className="flex items-start">
                    <FaMapMarkerAlt className="h-5 w-5 text-blue-400 mt-1 mr-3 flex-shrink-0" />
                    <p className="leading-relaxed">
                      No.5, Kumaran St,<br />
                      Pazhavanthangal,<br />
                      Tamil Nadu, India,<br />
                      Chennai-600 114.
                    </p>
                  </div>

                  <a href="tel:+************" className="flex items-center text-white hover:text-blue-400 transition-colors duration-300 font-medium font-['Open_Sans']">
                    <FaPhoneAlt className="h-4 w-4 text-blue-400 mr-3 flex-shrink-0" />
                    <span>+91 95000 97966</span>
                  </a>

                  <a href="mailto:<EMAIL>" className="flex items-center text-white hover:text-blue-400 transition-colors duration-300 font-medium font-['Open_Sans']">
                    <FaEnvelope className="h-4 w-4 text-blue-400 mr-3 flex-shrink-0" />
                    <span><EMAIL></span>
                  </a>

                  {/* Social Media Icons */}
                  <div className="flex space-x-3 pt-2">
                    <SocialButton
                      href="#"
                      icon={<FaLinkedin className="h-5 w-5 text-white" />}
                      label="LinkedIn"
                      color="bg-gradient-to-br from-blue-600 to-blue-500"
                    />
                    <SocialButton
                      href="#"
                      icon={<FaFacebook className="h-5 w-5 text-white" />}
                      label="Facebook"
                      color="bg-gradient-to-br from-blue-700 to-blue-500"
                    />
                    <SocialButton
                      href="#"
                      icon={<FaYoutube className="h-5 w-5 text-white" />}
                      label="YouTube"
                      color="bg-gradient-to-br from-red-600 to-red-500"
                    />
                  </div>
                </address>
              </motion.div>
            </div>

            {/* Bottom section with copyright */}
            <div className="mt-16">
              <div className="footer-divider mb-8"></div>
              <div className="flex flex-row justify-between items-center font-['Open_Sans']">
                <div className="flex items-center space-x-3">
                  <span className="text-base sm:text-lg font-light text-white opacity-80 font-['Open_Sans']">atandra</span>
                  <span className="text-base sm:text-lg font-bold text-white opacity-80 font-['Open_Sans']">KRYKARD</span>
                </div>

                <div className="text-base text-white text-right font-medium font-['Open_Sans']">
                  © {currentYear} All rights reserved KRYKARD, Powered by: Atandra Energy Private Limited.
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;